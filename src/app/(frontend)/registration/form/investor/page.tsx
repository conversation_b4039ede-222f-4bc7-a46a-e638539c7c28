"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { ArrowLeft, TrendingUp } from "lucide-react"
import { saveFormDataToStorage } from "@/lib/registration-utils"
import { GroupRegistration } from "@/components/registration/GroupRegistration"
import {
  initializeGroupRegistration,
  addGroupMember,
  updateGroupMember,
  removeGroupMember,
  navigateToMember,
  type GroupRegistrationState
} from "@/lib/group-registration-utils"

const investmentAreas = [
  "Traditional Foods & Nutrition",
  "Local Remedies & Traditional Medicine",
  "Musicology & Cultural Arts",
  "Cultural Tourism & Heritage",
  "Indigenous Technologies & Innovations",
  "Sui Generis Intellectual Property Systems",
]

const investmentTypes = [
  "Angel Investment",
  "Venture Capital",
  "Private Equity",
  "Impact Investment",
  "Grant Funding",
  "Strategic Partnership",
]

const investmentRanges = [
  "Under ksh 10,000",
  "ksh 10,000 - ksh 50,000",
  "ksh 50,000 - ksh 100,000",
  "ksh 100,000 - ksh 500,000",
  "ksh 500,000 - ksh 1,000,000",
  "Over ksh1,000,000",
]

const countries = [
  "Kenya",
  "Uganda",
  "Tanzania",
  "Rwanda",
  "Burundi",
  "South Sudan",
  "Ethiopia",
  "Somalia",
  "United States",
  "United Kingdom",
  "Other",
]

export default function InvestorRegistrationForm() {
  const router = useRouter()
  const [groupState, setGroupState] = useState<GroupRegistrationState>(initializeGroupRegistration())
  const [formData, setFormData] = useState({
    // Personal Information
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    organization: "",
    position: "",
    country: "",
    city: "",
    
    // Investment Profile
    investmentAreas: [] as string[],
    investmentTypes: [] as string[],
    investmentRange: "",
    investmentExperience: "",
    portfolioDescription: "",
    
    // Interests
    specificInterests: "",
    dueDiligenceRequirements: "",
    partnershipPreferences: "",
    
    // Background
    professionalBackground: "",
    previousInvestments: "",
    
    // Agreements
    termsAccepted: false,
    confidentialityAgreement: false,
    marketingConsent: false,
  })

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleInvestmentAreaChange = (area: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      investmentAreas: checked 
        ? [...prev.investmentAreas, area]
        : prev.investmentAreas.filter(a => a !== area)
    }))
  }

  const handleInvestmentTypeChange = (type: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      investmentTypes: checked 
        ? [...prev.investmentTypes, type]
        : prev.investmentTypes.filter(t => t !== type)
    }))
  }

  // Group registration handlers
  const handleToggleGroupMode = () => {
    setGroupState(prev => ({
      ...prev,
      isGroupMode: !prev.isGroupMode
    }))
  }

  const handleAddMember = () => {
    const newState = addGroupMember(groupState, formData)
    setGroupState(newState)
  }

  const handleNavigateToMember = (index: number) => {
    const newState = navigateToMember(groupState, groupState.currentMemberIndex, formData, index)
    setGroupState(newState)

    // Load the member's data into the form
    if (index < newState.members.length) {
      const memberData = newState.members[index] as any
      setFormData(memberData)
    } else {
      // New member - reset form
      setFormData({
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        organization: "",
        position: "",
        country: "",
        city: "",
        investmentAreas: [] as string[],
        investmentTypes: [] as string[],
        investmentRange: "",
        investmentExperience: "",
        portfolioDescription: "",
        specificInterests: "",
        dueDiligenceRequirements: "",
        partnershipPreferences: "",
        professionalBackground: "",
        previousInvestments: "",
        termsAccepted: false,
        confidentialityAgreement: false,
        marketingConsent: false,
      })
    }
  }

  const handleRemoveMember = (index: number) => {
    setGroupState(prev => removeGroupMember(prev, index))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Prepare registration data
    let registrationData

    if (groupState.isGroupMode) {
      // Update current member data and include all group members
      const updatedGroupState = updateGroupMember(groupState, groupState.currentMemberIndex, formData)
      registrationData = {
        registrationType: "Investment Discovery",
        isGroupRegistration: true,
        groupMembers: [...updatedGroupState.members, formData],
        submissionDate: new Date().toISOString()
      }
    } else {
      // Single registration
      registrationData = {
        ...formData,
        registrationType: "Investment Discovery",
        isGroupRegistration: false,
        submissionDate: new Date().toISOString()
      }
    }

    saveFormDataToStorage("ikia-registration", registrationData)

    console.log("Investor form submitted:", registrationData)
    router.push("/registration/success")
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Registration Types
        </Button>
        
        <div className="flex items-center space-x-4 mb-6">
          <div className="w-16 h-16 bg-gradient-to-br from-yellow-600 to-orange-600 rounded-2xl flex items-center justify-center">
            <TrendingUp className="w-8 h-8 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Investment Discovery Registration
            </h1>
            <p className="text-yellow-600 font-semibold">Explore Opportunities</p>
            <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-800">
                <strong>Note:</strong> This is a non-payment interest form designed to capture your investment preferences and facilitate matching with relevant opportunities. No payment is required at this stage.
              </p>
            </div>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Group Registration */}
        <GroupRegistration
          groupState={groupState}
          onToggleGroupMode={handleToggleGroupMode}
          onAddMember={handleAddMember}
          onNavigateToMember={handleNavigateToMember}
          onRemoveMember={handleRemoveMember}
          currentMemberData={formData}
        />

        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Personal Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange("firstName", e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="lastName">Last Name *</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange("lastName", e.target.value)}
                  required
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="organization">Organization/Fund</Label>
                <Input
                  id="organization"
                  value={formData.organization}
                  onChange={(e) => handleInputChange("organization", e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="position">Position/Title</Label>
                <Input
                  id="position"
                  value={formData.position}
                  onChange={(e) => handleInputChange("position", e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="country">Country *</Label>
                <Select value={formData.country} onValueChange={(value) => handleInputChange("country", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select your country" />
                  </SelectTrigger>
                  <SelectContent>
                    {countries.map((country) => (
                      <SelectItem key={country} value={country}>
                        {country}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="city">City/County *</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => handleInputChange("city", e.target.value)}
                  required
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Investment Profile */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Investment Profile
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label className="text-base font-semibold mb-4 block">
                Investment Areas of Interest (Select all that apply) *
              </Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {investmentAreas.map((area) => (
                  <div key={area} className="flex items-center space-x-2">
                    <Checkbox
                      id={area}
                      checked={formData.investmentAreas.includes(area)}
                      onCheckedChange={(checked) => handleInvestmentAreaChange(area, checked as boolean)}
                    />
                    <Label htmlFor={area} className="text-sm">
                      {area}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label className="text-base font-semibold mb-4 block">
                Investment Types (Select all that apply) *
              </Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {investmentTypes.map((type) => (
                  <div key={type} className="flex items-center space-x-2">
                    <Checkbox
                      id={type}
                      checked={formData.investmentTypes.includes(type)}
                      onCheckedChange={(checked) => handleInvestmentTypeChange(type, checked as boolean)}
                    />
                    <Label htmlFor={type} className="text-sm">
                      {type}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="investmentRange">Typical Investment Range</Label>
                <Select value={formData.investmentRange} onValueChange={(value) => handleInputChange("investmentRange", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select investment range" />
                  </SelectTrigger>
                  <SelectContent>
                    {investmentRanges.map((range) => (
                      <SelectItem key={range} value={range}>
                        {range}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
              </div>
            </div>

            <div>
              <Label htmlFor="portfolioDescription">Portfolio Description</Label>
              <Textarea
                id="portfolioDescription"
                placeholder="Describe your current investment portfolio and focus areas..."
                value={formData.portfolioDescription}
                onChange={(e) => handleInputChange("portfolioDescription", e.target.value)}
                rows={4}
              />
            </div>
          </CardContent>
        </Card>

        {/* Investment Interests */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Investment Interests & Requirements
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label htmlFor="specificInterests">Specific Investment Interests</Label>
              <Textarea
                id="specificInterests"
                placeholder="What specific types of Indigenous Knowledge projects or companies are you looking to invest in?"
                value={formData.specificInterests}
                onChange={(e) => handleInputChange("specificInterests", e.target.value)}
                rows={4}
              />
            </div>

            <div>
              <Label htmlFor="dueDiligenceRequirements">Due Diligence Requirements</Label>
              <Textarea
                id="dueDiligenceRequirements"
                placeholder="What are your typical due diligence requirements and processes?"
                value={formData.dueDiligenceRequirements}
                onChange={(e) => handleInputChange("dueDiligenceRequirements", e.target.value)}
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="partnershipPreferences">Partnership Preferences</Label>
              <Textarea
                id="partnershipPreferences"
                placeholder="What type of partnerships or involvement do you prefer with your investments?"
                value={formData.partnershipPreferences}
                onChange={(e) => handleInputChange("partnershipPreferences", e.target.value)}
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Continue Button */}
        <div className="flex justify-center">
          <Button
            type="submit"
            className="px-12 py-3 text-lg font-semibold"
            style={{ fontFamily: "Myriad Pro, Arial, sans-serif" }}
          >
            Continue to Review
          </Button>
        </div>
      </form>
    </div>
  )
}
