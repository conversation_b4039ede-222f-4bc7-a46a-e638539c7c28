"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { ArrowLeft, Store } from "lucide-react"
import { saveFormDataToStorage } from "@/lib/registration-utils"
import { GroupRegistration } from "@/components/registration/GroupRegistration"
import {
  initializeGroupRegistration,
  addGroupMember,
  updateGroupMember,
  removeGroupMember,
  navigateToMember,
  type GroupRegistrationState
} from "@/lib/group-registration-utils"

const businessTypes = [
  "Traditional Foods & Nutrition",
  "Local Remedies & Traditional Medicine",
  "Musicology & Cultural Arts",
  "Cultural Tourism & Heritage",
  "Indigenous Technologies & Innovations",
  "Sui Generis Intellectual Property Systems",
]

const countries = [
  "Kenya",
  "Uganda",
  "Tanzania",
  "Rwanda",
  "Burundi",
  "South Sudan",
  "Ethiopia",
  "Somalia",
  "United States",
  "United Kingdom",
  "Other",
]

const boothRequirements = [
  "Standard 3x3m booth",
  "Corner booth (additional cost)",
  "Double booth 6x3m (additional cost)",
  "Premium location (additional cost)",
]

const additionalServices = [
  "Audio/Visual equipment",
  "Additional furniture",
  "Electrical connections",
  "Internet connectivity",
  "Storage space",
  "Promotional materials display",
]

export default function ExhibitorRegistrationForm() {
  const router = useRouter()
  const [groupState, setGroupState] = useState<GroupRegistrationState>(initializeGroupRegistration())
  const [formData, setFormData] = useState({
    // Personal Information (Primary)
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    position: "",
    country: "",
    city: "",

    // Company/Business Information (Optional)
    hasCompany: false,
    companyName: "",
    website: "",
    address: "",
    businessType: "",
    businessDescription: "",
    productsServices: "",
    targetMarket: "",
    yearsInBusiness: "",

    // Exhibition Details
    boothRequirement: "",
    additionalServices: [] as string[],
    specialRequirements: "",

    // Additional Representatives (Optional)
    hasAdditionalReps: false,
    representative1Name: "",
    representative1Email: "",
    representative1Phone: "",
    representative2Name: "",
    representative2Email: "",
    representative2Phone: "",

    // Marketing
    companyLogo: null as File | null,
    marketingMaterials: "",
    mediaConsent: false,

    // Agreements
    termsAccepted: false,
    exhibitorGuidelines: false,
  })

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleServiceChange = (service: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      additionalServices: checked 
        ? [...prev.additionalServices, service]
        : prev.additionalServices.filter(s => s !== service)
    }))
  }

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setFormData(prev => ({
        ...prev,
        companyLogo: file
      }))
    }
  }

  // Group registration handlers
  const handleToggleGroupMode = () => {
    setGroupState(prev => ({
      ...prev,
      isGroupMode: !prev.isGroupMode
    }))
  }

  const handleAddMember = () => {
    const newState = addGroupMember(groupState, formData)
    setGroupState(newState)
  }

  const handleNavigateToMember = (index: number) => {
    const newState = navigateToMember(groupState, groupState.currentMemberIndex, formData, index)
    setGroupState(newState)

    // Load the member's data into the form
    if (index < newState.members.length) {
      const memberData = newState.members[index] as any
      setFormData(memberData)
    } else {
      // New member - reset form
      setFormData({
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        position: "",
        country: "",
        city: "",
        hasCompany: false,
        companyName: "",
        website: "",
        address: "",
        businessType: "",
        businessDescription: "",
        productsServices: "",
        targetMarket: "",
        yearsInBusiness: "",
        boothRequirement: "",
        additionalServices: [] as string[],
        specialRequirements: "",
        hasAdditionalReps: false,
        representative1Name: "",
        representative1Email: "",
        representative1Phone: "",
        representative2Name: "",
        representative2Email: "",
        representative2Phone: "",
        companyLogo: null as File | null,
        marketingMaterials: "",
        mediaConsent: false,
        termsAccepted: false,
        exhibitorGuidelines: false,
      })
    }
  }

  const handleRemoveMember = (index: number) => {
    setGroupState(prev => removeGroupMember(prev, index))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    // Prepare registration data
    let registrationData

    if (groupState.isGroupMode) {
      // Update current member data and include all group members
      const updatedGroupState = updateGroupMember(groupState, groupState.currentMemberIndex, formData)
      registrationData = {
        registrationType: "Exhibitor Registration",
        isGroupRegistration: true,
        groupMembers: [...updatedGroupState.members, formData],
        submissionDate: new Date().toISOString()
      }
    } else {
      // Single registration
      registrationData = {
        ...formData,
        registrationType: "Exhibitor Registration",
        isGroupRegistration: false,
        submissionDate: new Date().toISOString()
      }
    }

    saveFormDataToStorage("ikia-registration", registrationData)

    console.log("Exhibitor form submitted:", registrationData)
    router.push("/registration/success")
  }

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Registration Types
        </Button>
        
        <div className="flex items-center space-x-4 mb-6">
          <div className="w-16 h-16 bg-gradient-to-br from-amber-700 to-amber-800 rounded-2xl flex items-center justify-center">
            <Store className="w-8 h-8 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
                Exhibitor Registration
            </h1>
            <p className="text-amber-600 font-semibold">Showcase Your Innovation</p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Group Registration */}
        <GroupRegistration
          groupState={groupState}
          onToggleGroupMode={handleToggleGroupMode}
          onAddMember={handleAddMember}
          onNavigateToMember={handleNavigateToMember}
          onRemoveMember={handleRemoveMember}
          currentMemberData={formData}
        />

        {/* Personal Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Personal Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange("firstName", e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="lastName">Last Name *</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange("lastName", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="position">Position/Title</Label>
                <Input
                  id="position"
                  value={formData.position}
                  onChange={(e) => handleInputChange("position", e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="country">Country *</Label>
                <Select value={formData.country} onValueChange={(value) => handleInputChange("country", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select your country" />
                  </SelectTrigger>
                  <SelectContent>
                    {countries.map((country) => (
                      <SelectItem key={country} value={country}>
                        {country}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="city">City *</Label>
              <Input
                id="city"
                value={formData.city}
                onChange={(e) => handleInputChange("city", e.target.value)}
                required
              />
            </div>
          </CardContent>
        </Card>

        {/* Company/Business Information (Optional) */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Company/Business Information
            </CardTitle>
            <p className="text-sm text-gray-600 mt-2">
              Optional: Provide company details if you're representing a business or organization
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="hasCompany"
                checked={formData.hasCompany}
                onCheckedChange={(checked) => handleInputChange("hasCompany", checked)}
              />
              <Label htmlFor="hasCompany">I am representing a company/organization</Label>
            </div>

            {formData.hasCompany && (
              <>
                <div>
                  <Label htmlFor="companyName">Company/Organization Name *</Label>
                  <Input
                    id="companyName"
                    value={formData.companyName}
                    onChange={(e) => handleInputChange("companyName", e.target.value)}
                    required={formData.hasCompany}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="website">Website</Label>
                    <Input
                      id="website"
                      type="url"
                      value={formData.website}
                      onChange={(e) => handleInputChange("website", e.target.value)}
                      placeholder="https://www.example.com"
                    />
                  </div>
                  <div>
                    <Label htmlFor="businessType">Business Type</Label>
                    <Select value={formData.businessType} onValueChange={(value) => handleInputChange("businessType", value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select business type" />
                      </SelectTrigger>
                      <SelectContent>
                        {businessTypes.map((type) => (
                          <SelectItem key={type} value={type}>
                            {type}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="address">Business Address</Label>
                  <Input
                    id="address"
                    value={formData.address}
                    onChange={(e) => handleInputChange("address", e.target.value)}
                  />
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Business Details (when company is selected) */}
        {formData.hasCompany && (
          <Card>
            <CardHeader>
              <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
                Business Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="yearsInBusiness">Years in Business</Label>
                  <Input
                    id="yearsInBusiness"
                    value={formData.yearsInBusiness}
                    onChange={(e) => handleInputChange("yearsInBusiness", e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="targetMarket">Target Market</Label>
                  <Input
                    id="targetMarket"
                    value={formData.targetMarket}
                    onChange={(e) => handleInputChange("targetMarket", e.target.value)}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="businessDescription">Business Description</Label>
                <Textarea
                  id="businessDescription"
                  value={formData.businessDescription}
                  onChange={(e) => handleInputChange("businessDescription", e.target.value)}
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="productsServices">Products/Services Offered</Label>
                <Textarea
                  id="productsServices"
                  value={formData.productsServices}
                  onChange={(e) => handleInputChange("productsServices", e.target.value)}
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
        )}

        {/* Exhibition Requirements */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Exhibition Requirements
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label htmlFor="boothRequirement">Booth Size Requirement</Label>
              <Select value={formData.boothRequirement} onValueChange={(value) => handleInputChange("boothRequirement", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select booth size" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="small">Small (3x3m)</SelectItem>
                  <SelectItem value="medium">Medium (3x6m)</SelectItem>
                  <SelectItem value="large">Large (6x6m)</SelectItem>
                  <SelectItem value="custom">Custom Size</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="specialRequirements">Special Requirements</Label>
              <Textarea
                id="specialRequirements"
                value={formData.specialRequirements}
                onChange={(e) => handleInputChange("specialRequirements", e.target.value)}
                rows={3}
                placeholder="Any special setup, electrical, or accessibility requirements..."
              />
            </div>

            <div>
              <Label>Additional Services</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-2">
                {["Electricity", "Internet", "Furniture", "Audio/Visual", "Storage", "Catering"].map((service) => (
                  <div key={service} className="flex items-center space-x-2">
                    <Checkbox
                      id={service}
                      checked={formData.additionalServices.includes(service)}
                      onCheckedChange={(checked) => handleServiceChange(service, checked as boolean)}
                    />
                    <Label htmlFor={service} className="text-sm">{service}</Label>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Representatives (Optional) */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Additional Representatives
            </CardTitle>
            <p className="text-sm text-gray-600 mt-2">
              Optional: Add other team members who will be attending the exhibition
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="hasAdditionalReps"
                checked={formData.hasAdditionalReps}
                onCheckedChange={(checked) => handleInputChange("hasAdditionalReps", checked)}
              />
              <Label htmlFor="hasAdditionalReps">Add additional representatives</Label>
            </div>

            {formData.hasAdditionalReps && (
              <div className="space-y-6">
                <div className="p-4 border border-gray-200 rounded-lg">
                  <h4 className="font-semibold mb-4">Representative 1</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="representative1Name">Full Name</Label>
                      <Input
                        id="representative1Name"
                        value={formData.representative1Name}
                        onChange={(e) => handleInputChange("representative1Name", e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="representative1Email">Email</Label>
                      <Input
                        id="representative1Email"
                        type="email"
                        value={formData.representative1Email}
                        onChange={(e) => handleInputChange("representative1Email", e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="representative1Phone">Phone</Label>
                      <Input
                        id="representative1Phone"
                        type="tel"
                        value={formData.representative1Phone}
                        onChange={(e) => handleInputChange("representative1Phone", e.target.value)}
                      />
                    </div>
                  </div>
                </div>

                <div className="p-4 border border-gray-200 rounded-lg">
                  <h4 className="font-semibold mb-4">Representative 2</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="representative2Name">Full Name</Label>
                      <Input
                        id="representative2Name"
                        value={formData.representative2Name}
                        onChange={(e) => handleInputChange("representative2Name", e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="representative2Email">Email</Label>
                      <Input
                        id="representative2Email"
                        type="email"
                        value={formData.representative2Email}
                        onChange={(e) => handleInputChange("representative2Email", e.target.value)}
                      />
                    </div>
                    <div>
                      <Label htmlFor="representative2Phone">Phone</Label>
                      <Input
                        id="representative2Phone"
                        type="tel"
                        value={formData.representative2Phone}
                        onChange={(e) => handleInputChange("representative2Phone", e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Marketing & Media */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Marketing & Media
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label htmlFor="companyLogo">Company Logo</Label>
              <Input
                id="companyLogo"
                type="file"
                accept="image/*"
                onChange={handleLogoUpload}
              />
              <p className="text-sm text-gray-500 mt-1">Upload your company logo for exhibition materials</p>
            </div>

            <div>
              <Label htmlFor="marketingMaterials">Marketing Materials Description</Label>
              <Textarea
                id="marketingMaterials"
                value={formData.marketingMaterials}
                onChange={(e) => handleInputChange("marketingMaterials", e.target.value)}
                rows={3}
                placeholder="Describe any brochures, banners, or promotional materials you'll bring..."
              />
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="mediaConsent"
                checked={formData.mediaConsent}
                onCheckedChange={(checked) => handleInputChange("mediaConsent", checked)}
              />
              <Label htmlFor="mediaConsent">
                I consent to photography and media coverage during the exhibition
              </Label>
            </div>
          </CardContent>
        </Card>

        {/* Terms and Agreements */}
        <Card>
          <CardHeader>
            <CardTitle className="text-xl font-bold" style={{ fontFamily: 'ACQUIRE, Arial Black, Impact, sans-serif' }}>
              Terms and Agreements
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-start space-x-2">
              <Checkbox
                id="termsAccepted"
                checked={formData.termsAccepted}
                onCheckedChange={(checked) => handleInputChange("termsAccepted", checked)}
                required
              />
              <Label htmlFor="termsAccepted" className="text-sm">
                I accept the <a href="#" className="text-blue-600 hover:underline">Terms and Conditions</a> for exhibition participation *
              </Label>
            </div>

            <div className="flex items-start space-x-2">
              <Checkbox
                id="exhibitorGuidelines"
                checked={formData.exhibitorGuidelines}
                onCheckedChange={(checked) => handleInputChange("exhibitorGuidelines", checked)}
                required
              />
              <Label htmlFor="exhibitorGuidelines" className="text-sm">
                I have read and agree to follow the <a href="#" className="text-blue-600 hover:underline">Exhibitor Guidelines</a> *
              </Label>
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-center">
          <Button
            type="submit"
            size="lg"
            className="bg-amber-600 hover:bg-amber-700 text-white px-8 py-3"
            disabled={!formData.termsAccepted || !formData.exhibitorGuidelines}
          >
            Submit Exhibition Registration
          </Button>
        </div>
      </form>
    </div>
  )
}


