'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Search, ChevronLeft, ChevronRight, Phone, Mail } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'

function NewsMediaContent() {
  const [activeTab, setActiveTab] = useState('news-feed')

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Sub Navigation */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8 py-4">
            <button
              onClick={() => setActiveTab('news-feed')}
              className={`font-medium pb-2 transition-colors ${
                activeTab === 'news-feed'
                  ? 'text-red-600 border-b-2 border-red-600'
                  : 'text-gray-600 hover:text-red-600'
              }`}
            >
              News feed
            </button>
            <button
              onClick={() => setActiveTab('press-releases')}
              className={`font-medium pb-2 transition-colors ${
                activeTab === 'press-releases'
                  ? 'text-red-600 border-b-2 border-red-600'
                  : 'text-gray-600 hover:text-red-600'
              }`}
            >
              Press releases
            </button>
            <button
              onClick={() => setActiveTab('blogs')}
              className={`font-medium pb-2 transition-colors ${
                activeTab === 'blogs'
                  ? 'text-red-600 border-b-2 border-red-600'
                  : 'text-gray-600 hover:text-red-600'
              }`}
            >
              Blogs
            </button>
            <button
              onClick={() => setActiveTab('social-media')}
              className={`font-medium pb-2 transition-colors ${
                activeTab === 'social-media'
                  ? 'text-red-600 border-b-2 border-red-600'
                  : 'text-gray-600 hover:text-red-600'
              }`}
            >
              Social Media
            </button>
            <button
              onClick={() => setActiveTab('events-updates')}
              className={`font-medium pb-2 transition-colors ${
                activeTab === 'events-updates'
                  ? 'text-red-600 border-b-2 border-red-600'
                  : 'text-gray-600 hover:text-red-600'
              }`}
            >
              Events updates
            </button>
          </nav>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'news-feed' && <NewsFeedContent />}
        {activeTab === 'press-releases' && <PressReleasesContent />}
        {activeTab === 'blogs' && <BlogsContent />}
        {activeTab === 'social-media' && <SocialMediaContent />}
        {activeTab === 'events-updates' && <EventsUpdatesContent />}

        {/* Featured Banner Slider - Show on all tabs */}
        <div className="mb-12">
          <SummitSlider />
        </div>

        {/* Newsletter Section - Show on all tabs */}
        <div className="bg-white rounded-lg p-8 mb-12 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">Join Our Newsletter</h3>
          <p className="text-gray-600 mb-6">
            Stay updated with the latest business news and insights
          </p>
          <div className="max-w-md mx-auto flex space-x-4">
            <Input type="email" placeholder="Enter your email address" className="flex-1" />
            <Button className="bg-red-600 hover:bg-red-700">Subscribe</Button>
          </div>
        </div>
      </div>


    </div>
  )
}

export default function NewsMediaPage() {
  return <NewsMediaContent />
}

function HeadlinesSlider() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  const headlines = [
    {
      title: 'Breaking: Major Economic Policy Changes Announced',
      description:
        'Government unveils comprehensive economic reform package aimed at boosting business growth and investment opportunities.',
      time: '2 hours ago',
      author: 'Business Editor',
      image: '/public/placeholder.svg?height=500&width=800',
      slug: 'economic-policy-changes',
    },
    {
      title: 'Tech Giants Report Record Quarterly Earnings',
      description:
        'Leading technology companies exceed market expectations with unprecedented revenue growth across all sectors.',
      time: '4 hours ago',
      author: 'Tech Reporter',
      image: '/placeholder.svg?height=500&width=800',
      slug: 'tech-earnings-report',
    },
    {
      title: 'Global Markets Show Strong Recovery Signs',
      description:
        'International financial markets demonstrate resilience with sustained growth patterns emerging across major economies.',
      time: '6 hours ago',
      author: 'Market Analyst',
      image: '/placeholder.svg?height=500&width=800',
      slug: 'market-recovery-signs',
    },
  ]

  const nextSlide = () => {
    if (!isAnimating) {
      setIsAnimating(true)
      setCurrentSlide((prev) => (prev + 1) % headlines.length)
      setTimeout(() => setIsAnimating(false), 1000)
    }
  }

  const goToSlide = (index: number) => {
    if (!isAnimating && index !== currentSlide) {
      setIsAnimating(true)
      setCurrentSlide(index)
      setTimeout(() => setIsAnimating(false), 1000)
    }
  }

  useEffect(() => {
    const timer = setInterval(() => {
      nextSlide()
    }, 6000)
    return () => clearInterval(timer)
  }, [])

  return (
    <div className="relative">
      <Card className="overflow-hidden">
        <div className="relative h-[500px]">
          {headlines.map((headline, index) => {
            const isActive = index === currentSlide
            const isPrev = index === (currentSlide - 1 + headlines.length) % headlines.length
            const isNext = index === (currentSlide + 1) % headlines.length

            let slideClass = 'absolute inset-0 transition-all duration-1000 ease-in-out'

            if (isActive) {
              slideClass += ' opacity-100 transform translate-x-0 scale-100'
            } else if (isNext) {
              slideClass += ' opacity-0 transform translate-x-full scale-95'
            } else if (isPrev) {
              slideClass += ' opacity-0 transform -translate-x-full scale-95'
            } else {
              slideClass += ' opacity-0 transform translate-x-full scale-95'
            }

            return (
              <Link key={index} href={`/article/${headline.slug}`}>
                <div className={slideClass}>
                  <div className="relative w-full h-full overflow-hidden cursor-pointer">
                    <Image
                      src={headline.image || '/placeholder.svg'}
                      alt={headline.title}
                      fill
                      className="object-cover transition-transform duration-1000 ease-out hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
                    <div className="absolute bottom-8 left-8 right-8 text-white">
                      <div
                        className={`transform transition-all duration-1000 delay-300 ${
                          isActive ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
                        }`}
                      >
                        <h3 className="text-3xl font-bold mb-3 leading-tight">{headline.title}</h3>
                        <p className="text-gray-200 mb-3 text-lg leading-relaxed">
                          {headline.description}
                        </p>
                        <div className="flex items-center space-x-4 text-sm">
                          <span className="bg-red-600 px-3 py-1 rounded-full">{headline.time}</span>
                          <span>•</span>
                          <span className="font-medium">{headline.author}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            )
          })}
        </div>
      </Card>

      {/* Enhanced slide indicators */}
      <div className="flex justify-center space-x-3 mt-6">
        {headlines.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`relative overflow-hidden transition-all duration-500 ${
              index === currentSlide
                ? 'w-8 h-3 bg-red-600 rounded-full'
                : 'w-3 h-3 bg-gray-300 hover:bg-gray-400 rounded-full'
            }`}
          >
            {index === currentSlide && (
              <div className="absolute inset-0 bg-red-700 rounded-full animate-pulse" />
            )}
          </button>
        ))}
      </div>
    </div>
  )
}

function SummitSlider() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  const summits = [
    {
      title: 'Join Our Upcoming Business Summit',
      description: 'Connect with industry leaders and expand your network',
      image: '/placeholder.svg?height=300&width=800',
      bgColor: 'from-red-600 to-red-800',
    },
    {
      title: 'Entrepreneurship Masterclass Series',
      description: 'Learn from successful entrepreneurs and scale your business',
      image: '/placeholder.svg?height=300&width=800',
      bgColor: 'from-blue-600 to-blue-800',
    },
    {
      title: 'Investment Opportunities Forum',
      description: 'Discover the latest investment trends and opportunities',
      image: '/placeholder.svg?height=300&width=800',
      bgColor: 'from-green-600 to-green-800',
    },
    {
      title: 'Digital Transformation Workshop',
      description: 'Transform your business with cutting-edge technology',
      image: '/placeholder.svg?height=300&width=800',
      bgColor: 'from-purple-600 to-purple-800',
    },
  ]

  const nextSlide = () => {
    if (!isAnimating) {
      setIsAnimating(true)
      setCurrentSlide((prev) => (prev + 1) % summits.length)
      setTimeout(() => setIsAnimating(false), 1200)
    }
  }

  const prevSlide = () => {
    if (!isAnimating) {
      setIsAnimating(true)
      setCurrentSlide((prev) => (prev - 1 + summits.length) % summits.length)
      setTimeout(() => setIsAnimating(false), 1200)
    }
  }

  const goToSlide = (index: number) => {
    if (!isAnimating && index !== currentSlide) {
      setIsAnimating(true)
      setCurrentSlide(index)
      setTimeout(() => setIsAnimating(false), 1200)
    }
  }

  useEffect(() => {
    const timer = setInterval(() => {
      nextSlide()
    }, 7000)
    return () => clearInterval(timer)
  }, [])

  return (
    <Card className="overflow-hidden">
      <div className="relative h-80">
        {summits.map((summit, index) => {
          const isActive = index === currentSlide
          const isPrev = index === (currentSlide - 1 + summits.length) % summits.length
          const isNext = index === (currentSlide + 1) % summits.length

          let slideClass = 'absolute inset-0 transition-all duration-1200 ease-in-out'

          if (isActive) {
            slideClass += ' opacity-100 transform translate-x-0 scale-100'
          } else if (isNext) {
            slideClass += ' opacity-0 transform translate-x-full scale-95'
          } else if (isPrev) {
            slideClass += ' opacity-0 transform -translate-x-full scale-95'
          } else {
            slideClass += ' opacity-0 transform translate-x-full scale-95'
          }

          return (
            <div key={index} className={slideClass}>
              <div className={`absolute inset-0 bg-gradient-to-r ${summit.bgColor}`} />
              <Image
                src={summit.image || '/placeholder.svg'}
                alt={summit.title}
                fill
                className="object-cover opacity-30 transition-all duration-1200 ease-out"
              />
              <div className="absolute inset-0 flex items-center justify-between px-8">
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-white hover:bg-white/20 z-10 transition-all duration-300 hover:scale-110"
                  onClick={prevSlide}
                  disabled={isAnimating}
                >
                  <ChevronLeft className="w-6 h-6" />
                </Button>
                <div
                  className={`text-center text-white transform transition-all duration-1000 delay-200 ${
                    isActive
                      ? 'translate-y-0 opacity-100 scale-100'
                      : 'translate-y-4 opacity-0 scale-95'
                  }`}
                >
                  <h3 className="text-3xl font-bold mb-3">{summit.title}</h3>
                  <p className="text-gray-100 text-lg">{summit.description}</p>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-white hover:bg-white/20 z-10 transition-all duration-300 hover:scale-110"
                  onClick={nextSlide}
                  disabled={isAnimating}
                >
                  <ChevronRight className="w-6 h-6" />
                </Button>
              </div>
            </div>
          )
        })}

        {/* Enhanced slide indicators */}
        <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-3">
          {summits.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              disabled={isAnimating}
              className={`relative overflow-hidden transition-all duration-500 ${
                index === currentSlide
                  ? 'w-8 h-2 bg-white rounded-full'
                  : 'w-2 h-2 bg-white/50 hover:bg-white/70 rounded-full'
              }`}
            >
              {index === currentSlide && (
                <div className="absolute inset-0 bg-white/80 rounded-full animate-pulse" />
              )}
            </button>
          ))}
        </div>
      </div>
    </Card>
  )
}

function NewsFeedContent() {
  return (
    <>
      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
        {/* Headlines Section */}
        <div className="lg:col-span-2">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Headlines</h2>
          <div className="ml-4">
            <HeadlinesSlider />
          </div>
        </div>

        {/* Latest Topics Section */}
        <div>
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Latest Topics</h2>
          <div className="space-y-4">
            {[
              { id: 1, slug: 'economic-policy-changes', title: 'Economic Policy Session' },
              { id: 2, slug: 'tech-earnings-report', title: 'Tech Earnings Session' },
              { id: 3, slug: 'market-recovery-signs', title: 'Market Recovery Session' },
              { id: 4, slug: 'investment-opportunities', title: 'Investment Session' },
            ].map((item) => (
              <Link key={item.id} href={`/article/${item.slug}`}>
                <Card className="p-4 hover:shadow-lg transition-shadow cursor-pointer">
                  <div className="flex space-x-4">
                    <div className="w-16 h-16 bg-gray-200 rounded flex-shrink-0">
                      <Image
                        src="/placeholder.svg?height=64&width=64"
                        alt="Topic thumbnail"
                        width={64}
                        height={64}
                        className="rounded object-cover"
                      />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-1">{item.title}</h4>
                      <p className="text-sm text-gray-600 line-clamp-3">
                        The NPI Initiative is a transformative national program aligned with Kenya
                        Vision 2030, BaTA, and NMK's Strategic Plan. The NPI Initiative is a
                        transformative national program aligned with Kenya Vision 2030, BaTA, and
                        NMK's Strategic Plan...
                      </p>
                    </div>
                  </div>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Latest News Section */}
      <div className="mb-12">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">Latest News</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            {
              id: 1,
              slug: 'economic-policy-changes',
              title:
                'The NPI Initiative is a transformative national program aligned with Kenya Vision 2030',
            },
            {
              id: 2,
              slug: 'tech-earnings-report',
              title: 'Technology sector shows unprecedented growth in quarterly reports',
            },
            {
              id: 3,
              slug: 'market-recovery-signs',
              title: 'Global markets demonstrate strong recovery patterns across sectors',
            },
            {
              id: 4,
              slug: 'investment-opportunities',
              title: 'New investment opportunities emerge in developing markets',
            },
            {
              id: 5,
              slug: 'digital-transformation',
              title: 'Digital transformation accelerates across traditional industries',
            },
            {
              id: 6,
              slug: 'sustainable-business',
              title: 'Sustainable business practices gain momentum worldwide',
            },
          ].map((item) => (
            <Link key={item.id} href={`/article/${item.slug}`}>
              <Card className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                <div className="relative h-48">
                  <Image
                    src={`/placeholder.svg?height=200&width=300&query=business news story ${item.id}`}
                    alt={`News story ${item.id}`}
                    fill
                    className="object-cover"
                  />
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">{item.title}</h3>
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>21 days ago</span>
                    <span>Connor Walcott</span>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </>
  )
}

function PressReleasesContent() {
  return (
    <>
      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
        {/* Featured Press Release */}
        <div className="lg:col-span-2">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Featured Press Release</h2>
          <div className="ml-4">
            <PressReleaseSlider />
          </div>
        </div>

        {/* Recent Press Releases */}
        <div>
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Recent Releases</h2>
          <div className="space-y-6">
            {[
              {
                id: 1,
                title: 'Q4 Financial Results',
                type: 'Financial',
                slug: 'q4-financial-results',
              },
              { id: 2, title: 'New Product Launch', type: 'Product', slug: 'new-product-launch' },
              {
                id: 3,
                title: 'Executive Appointment',
                type: 'Personnel',
                slug: 'executive-appointment',
              },
              { id: 4, title: 'Market Expansion', type: 'Business', slug: 'market-expansion' },
            ].map((item) => (
              <Link key={item.id} href={`/press-release/${item.slug}`}>
                <Card className="p-4 hover:shadow-lg transition-shadow cursor-pointer">
                  <div className="flex space-x-4">
                    <div className="w-16 h-16 bg-blue-100 rounded flex-shrink-0 flex items-center justify-center">
                      <span className="text-blue-600 font-semibold text-xs">{item.type}</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-1">{item.title}</h4>
                      <p className="text-sm text-gray-600 line-clamp-3">
                        Official company announcement regarding recent developments and strategic
                        initiatives that impact stakeholders and market position.
                      </p>
                    </div>
                  </div>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* All Press Releases */}
      <div className="mb-12">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">All Press Releases</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            { id: 1, slug: 'strategic-partnership-announcement' },
            { id: 2, slug: 'q4-financial-results' },
            { id: 3, slug: 'new-product-launch' },
            { id: 4, slug: 'executive-appointment' },
            { id: 5, slug: 'market-expansion' },
            { id: 6, slug: 'corporate-sustainability-report' },
          ].map((item) => (
            <Link key={item.id} href={`/press-release/${item.slug}`}>
              <Card className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                <div className="relative h-48">
                  <Image
                    src={`/placeholder.svg?height=200&width=300&query=corporate press release ${item.id}`}
                    alt={`Press release ${item.id}`}
                    fill
                    className="object-cover"
                  />
                </div>
                <CardContent className="p-4">
                  <div className="flex items-center mb-2">
                    <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                      Press Release
                    </span>
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                    Strategic Initiative Announcement for Market Expansion
                  </h3>
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>3 days ago</span>
                    <span>Corporate Team</span>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </>
  )
}

function BlogsContent() {
  return (
    <>
      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
        {/* Featured Blog Post */}
        <div className="lg:col-span-2">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Featured Blog Post</h2>
          <div className="ml-4">
            <BlogSlider />
          </div>
        </div>

        {/* Popular Topics */}
        <div>
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Popular Topics</h2>
          <div className="space-y-6">
            {[
              {
                id: 1,
                title: 'Leadership Insights',
                category: 'Management',
                slug: 'leadership-insights',
              },
              {
                id: 2,
                title: 'Innovation Strategies',
                category: 'Technology',
                slug: 'innovation-strategies',
              },
              { id: 3, title: 'Market Analysis', category: 'Finance', slug: 'market-analysis' },
              {
                id: 4,
                title: 'Startup Stories',
                category: 'Entrepreneurship',
                slug: 'startup-stories',
              },
            ].map((item) => (
              <Link key={item.id} href={`/blog/${item.slug}`}>
                <Card className="p-4 hover:shadow-lg transition-shadow cursor-pointer">
                  <div className="flex space-x-4">
                    <div className="w-16 h-16 bg-green-100 rounded flex-shrink-0 flex items-center justify-center">
                      <span className="text-green-600 font-semibold text-xs">{item.category}</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-1">{item.title}</h4>
                      <p className="text-sm text-gray-600 line-clamp-3">
                        In-depth analysis and insights from industry experts covering the latest
                        trends and best practices in business.
                      </p>
                    </div>
                  </div>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Latest Blog Posts */}
      <div className="mb-12">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">Latest Blog Posts</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            { id: 1, slug: 'essential-business-strategies' },
            { id: 2, slug: 'navigating-digital-transformation' },
            { id: 3, slug: 'sustainable-business-practices' },
            { id: 4, slug: 'future-of-workplace' },
            { id: 5, slug: 'entrepreneur-success-stories' },
            { id: 6, slug: 'technology-transforming-business' },
          ].map((item) => (
            <Link key={item.id} href={`/blog/${item.slug}`}>
              <Card className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                <div className="relative h-48">
                  <Image
                    src={`/placeholder.svg?height=200&width=300&query=business blog post ${item.id}`}
                    alt={`Blog post ${item.id}`}
                    fill
                    className="object-cover"
                  />
                </div>
                <CardContent className="p-4">
                  <div className="flex items-center mb-2">
                    <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                      Blog
                    </span>
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                    Essential Business Strategies for Modern Entrepreneurs
                  </h3>
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>1 week ago</span>
                    <span>Blog Editor</span>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </>
  )
}

function SocialMediaContent() {
  return (
    <>
      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
        {/* Trending Social Post */}
        <div className="lg:col-span-2">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Trending Social Post</h2>
          <div className="ml-4">
            <SocialMediaSlider />
          </div>
        </div>

        {/* Social Highlights */}
        <div>
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Social Highlights</h2>
          <div className="space-y-6">
            {[
              { id: 1, title: 'LinkedIn Update', platform: 'LinkedIn', slug: 'linkedin-update' },
              { id: 2, title: 'Twitter Thread', platform: 'Twitter', slug: 'twitter-thread' },
              { id: 3, title: 'Instagram Story', platform: 'Instagram', slug: 'instagram-story' },
              { id: 4, title: 'Facebook Post', platform: 'Facebook', slug: 'facebook-post' },
            ].map((item) => (
              <Link key={item.id} href={`/social/${item.slug}`}>
                <Card className="p-4 hover:shadow-lg transition-shadow cursor-pointer">
                  <div className="flex space-x-4">
                    <div className="w-16 h-16 bg-purple-100 rounded flex-shrink-0 flex items-center justify-center">
                      <span className="text-purple-600 font-semibold text-xs">{item.platform}</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-1">{item.title}</h4>
                      <p className="text-sm text-gray-600 line-clamp-3">
                        Engaging content from our social media channels featuring industry insights
                        and company updates.
                      </p>
                    </div>
                  </div>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Social Media Feed */}
      <div className="mb-12">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">Social Media Feed</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            { id: 1, slug: 'behind-the-scenes' },
            { id: 2, slug: 'entrepreneur-spotlight' },
            { id: 3, slug: 'innovation-showcase' },
            { id: 4, slug: 'community-engagement' },
            { id: 5, slug: 'business-insights' },
            { id: 6, slug: 'company-updates' },
          ].map((item) => (
            <Link key={item.id} href={`/social/${item.slug}`}>
              <Card className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                <div className="relative h-48">
                  <Image
                    src={`/placeholder.svg?height=200&width=300&query=social media business post ${item.id}`}
                    alt={`Social media post ${item.id}`}
                    fill
                    className="object-cover"
                  />
                </div>
                <CardContent className="p-4">
                  <div className="flex items-center mb-2">
                    <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">
                      Social
                    </span>
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                    Inspiring Business Moments from Our Community
                  </h3>
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>2 hours ago</span>
                    <span>Social Team</span>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </>
  )
}

function EventsUpdatesContent() {
  return (
    <>
      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
        {/* Featured Event */}
        <div className="lg:col-span-2">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Featured Event</h2>
          <div className="ml-4">
            <EventsSlider />
          </div>
        </div>

        {/* Upcoming Events */}
        <div>
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Upcoming Events</h2>
          <div className="space-y-6">
            {[
              {
                id: 1,
                title: 'Tech Innovation Workshop',
                date: 'Feb 20',
                slug: 'tech-innovation-workshop',
              },
              {
                id: 2,
                title: 'Leadership Masterclass',
                date: 'Feb 25',
                slug: 'leadership-masterclass',
              },
              {
                id: 3,
                title: 'Startup Pitch Competition',
                date: 'Mar 5',
                slug: 'startup-pitch-competition',
              },
              {
                id: 4,
                title: 'Digital Marketing Summit',
                date: 'Mar 12',
                slug: 'digital-marketing-summit',
              },
            ].map((item) => (
              <Link key={item.id} href={`/event/${item.slug}`}>
                <Card className="p-4 hover:shadow-lg transition-shadow cursor-pointer">
                  <div className="flex space-x-4">
                    <div className="w-16 h-16 bg-orange-100 rounded flex-shrink-0 flex items-center justify-center">
                      <span className="text-orange-600 font-semibold text-xs">{item.date}</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-1">{item.title}</h4>
                      <p className="text-sm text-gray-600 line-clamp-3">
                        Professional development and networking opportunities designed to advance
                        your business knowledge and connections.
                      </p>
                    </div>
                  </div>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* All Events */}
      <div className="mb-12">
        <h2 className="text-3xl font-bold text-gray-900 mb-8">All Events</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            { id: 1, slug: 'global-business-summit-2024' },
            { id: 2, slug: 'tech-innovation-workshop' },
            { id: 3, slug: 'leadership-masterclass' },
            { id: 4, slug: 'startup-pitch-competition' },
            { id: 5, slug: 'digital-marketing-summit' },
            { id: 6, slug: 'corporate-networking-event' },
          ].map((item) => (
            <Link key={item.id} href={`/event/${item.slug}`}>
              <Card className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                <div className="relative h-48">
                  <Image
                    src={`/placeholder.svg?height=200&width=300&query=business event conference ${item.id}`}
                    alt={`Event ${item.id}`}
                    fill
                    className="object-cover"
                  />
                </div>
                <CardContent className="p-4">
                  <div className="flex items-center mb-2">
                    <span className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                      Event
                    </span>
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                    Professional Development Conference and Networking
                  </h3>
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>Next month</span>
                    <span>Event Team</span>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </>
  )
}

function PressReleaseSlider() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  const pressReleases = [
    {
      title: 'Company Announces Strategic Partnership with Global Tech Leader',
      description:
        'This groundbreaking partnership will accelerate innovation and expand market reach across multiple sectors.',
      time: '1 hour ago',
      author: 'Corporate Communications',
      image: '/placeholder.svg?height=500&width=800',
      slug: 'strategic-partnership-announcement',
    },
    {
      title: 'Q4 Financial Results Exceed Market Expectations',
      description:
        'Strong performance across all business units drives record revenue and profitability for the quarter.',
      time: '3 hours ago',
      author: 'Investor Relations',
      image: '/placeholder.svg?height=500&width=800',
      slug: 'q4-financial-results',
    },
    {
      title: 'New Product Line Launch Revolutionizes Industry Standards',
      description:
        'Innovative technology platform sets new benchmarks for efficiency and user experience in the market.',
      time: '1 day ago',
      author: 'Product Marketing',
      image: '/placeholder.svg?height=500&width=800',
      slug: 'new-product-launch',
    },
  ]

  const nextSlide = () => {
    if (!isAnimating) {
      setIsAnimating(true)
      setCurrentSlide((prev) => (prev + 1) % pressReleases.length)
      setTimeout(() => setIsAnimating(false), 1000)
    }
  }

  const goToSlide = (index: number) => {
    if (!isAnimating && index !== currentSlide) {
      setIsAnimating(true)
      setCurrentSlide(index)
      setTimeout(() => setIsAnimating(false), 1000)
    }
  }

  useEffect(() => {
    const timer = setInterval(() => {
      nextSlide()
    }, 6000)
    return () => clearInterval(timer)
  }, [])

  return (
    <div className="relative">
      <Card className="overflow-hidden">
        <div className="relative h-[500px]">
          {pressReleases.map((release, index) => {
            const isActive = index === currentSlide
            const isPrev =
              index === (currentSlide - 1 + pressReleases.length) % pressReleases.length
            const isNext = index === (currentSlide + 1) % pressReleases.length

            let slideClass = 'absolute inset-0 transition-all duration-1000 ease-in-out'

            if (isActive) {
              slideClass += ' opacity-100 transform translate-x-0 scale-100'
            } else if (isNext) {
              slideClass += ' opacity-0 transform translate-x-full scale-95'
            } else if (isPrev) {
              slideClass += ' opacity-0 transform -translate-x-full scale-95'
            } else {
              slideClass += ' opacity-0 transform translate-x-full scale-95'
            }

            return (
              <Link key={index} href={`/press-release/${release.slug}`}>
                <div className={slideClass}>
                  <div className="relative w-full h-full overflow-hidden cursor-pointer">
                    <Image
                      src={release.image || '/placeholder.svg'}
                      alt={release.title}
                      fill
                      className="object-cover transition-transform duration-1000 ease-out hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
                    <div className="absolute bottom-8 left-8 right-8 text-white">
                      <div
                        className={`transform transition-all duration-1000 delay-300 ${
                          isActive ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
                        }`}
                      >
                        <h3 className="text-3xl font-bold mb-3 leading-tight">{release.title}</h3>
                        <p className="text-gray-200 mb-3 text-lg leading-relaxed">
                          {release.description}
                        </p>
                        <div className="flex items-center space-x-4 text-sm">
                          <span className="bg-blue-600 px-3 py-1 rounded-full">{release.time}</span>
                          <span>•</span>
                          <span className="font-medium">{release.author}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            )
          })}
        </div>
      </Card>

      <div className="flex justify-center space-x-3 mt-6">
        {pressReleases.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`relative overflow-hidden transition-all duration-500 ${
              index === currentSlide
                ? 'w-8 h-3 bg-blue-600 rounded-full'
                : 'w-3 h-3 bg-gray-300 hover:bg-gray-400 rounded-full'
            }`}
          >
            {index === currentSlide && (
              <div className="absolute inset-0 bg-blue-700 rounded-full animate-pulse" />
            )}
          </button>
        ))}
      </div>
    </div>
  )
}

function BlogSlider() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  const blogPosts = [
    {
      title: "The Future of Business: Trends Shaping Tomorrow's Economy",
      description:
        'Explore the key trends and innovations that will define the business landscape in the coming decade.',
      time: '5 min read',
      author: 'Business Analyst',
      image: '/placeholder.svg?height=500&width=800',
      slug: 'future-of-business-trends',
    },
    {
      title: 'Leadership in the Digital Age: Adapting to Change',
      description:
        'How modern leaders can navigate digital transformation while maintaining human connection and purpose.',
      time: '7 min read',
      author: 'Leadership Expert',
      image: '/placeholder.svg?height=500&width=800',
      slug: 'leadership-digital-age',
    },
    {
      title: 'Sustainable Business Practices: Profit with Purpose',
      description:
        'Discover how companies are integrating sustainability into their core business strategies for long-term success.',
      time: '6 min read',
      author: 'Sustainability Consultant',
      image: '/placeholder.svg?height=500&width=800',
      slug: 'sustainable-business-practices',
    },
  ]

  const nextSlide = () => {
    if (!isAnimating) {
      setIsAnimating(true)
      setCurrentSlide((prev) => (prev + 1) % blogPosts.length)
      setTimeout(() => setIsAnimating(false), 1000)
    }
  }

  const goToSlide = (index: number) => {
    if (!isAnimating && index !== currentSlide) {
      setIsAnimating(true)
      setCurrentSlide(index)
      setTimeout(() => setIsAnimating(false), 1000)
    }
  }

  useEffect(() => {
    const timer = setInterval(() => {
      nextSlide()
    }, 6000)
    return () => clearInterval(timer)
  }, [])

  return (
    <div className="relative">
      <Card className="overflow-hidden">
        <div className="relative h-[500px]">
          {blogPosts.map((post, index) => {
            const isActive = index === currentSlide
            const isPrev = index === (currentSlide - 1 + blogPosts.length) % blogPosts.length
            const isNext = index === (currentSlide + 1) % blogPosts.length

            let slideClass = 'absolute inset-0 transition-all duration-1000 ease-in-out'

            if (isActive) {
              slideClass += ' opacity-100 transform translate-x-0 scale-100'
            } else if (isNext) {
              slideClass += ' opacity-0 transform translate-x-full scale-95'
            } else if (isPrev) {
              slideClass += ' opacity-0 transform -translate-x-full scale-95'
            } else {
              slideClass += ' opacity-0 transform translate-x-full scale-95'
            }

            return (
              <Link key={index} href={`/blog/${post.slug}`}>
                <div className={slideClass}>
                  <div className="relative w-full h-full overflow-hidden cursor-pointer">
                    <Image
                      src={post.image || '/placeholder.svg'}
                      alt={post.title}
                      fill
                      className="object-cover transition-transform duration-1000 ease-out hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
                    <div className="absolute bottom-8 left-8 right-8 text-white">
                      <div
                        className={`transform transition-all duration-1000 delay-300 ${
                          isActive ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
                        }`}
                      >
                        <h3 className="text-3xl font-bold mb-3 leading-tight">{post.title}</h3>
                        <p className="text-gray-200 mb-3 text-lg leading-relaxed">
                          {post.description}
                        </p>
                        <div className="flex items-center space-x-4 text-sm">
                          <span className="bg-green-600 px-3 py-1 rounded-full">{post.time}</span>
                          <span>•</span>
                          <span className="font-medium">{post.author}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            )
          })}
        </div>
      </Card>

      <div className="flex justify-center space-x-3 mt-6">
        {blogPosts.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`relative overflow-hidden transition-all duration-500 ${
              index === currentSlide
                ? 'w-8 h-3 bg-green-600 rounded-full'
                : 'w-3 h-3 bg-gray-300 hover:bg-gray-400 rounded-full'
            }`}
          >
            {index === currentSlide && (
              <div className="absolute inset-0 bg-green-700 rounded-full animate-pulse" />
            )}
          </button>
        ))}
      </div>
    </div>
  )
}

function SocialMediaSlider() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  const socialPosts = [
    {
      title: "Behind the Scenes: Building Tomorrow's Workplace",
      description:
        'Take a look at how innovative companies are reshaping the future of work and employee engagement.',
      time: '2.5K likes',
      author: 'Social Media Team',
      image: '/placeholder.svg?height=500&width=800',
      slug: 'building-tomorrows-workplace',
    },
    {
      title: 'Entrepreneur Spotlight: Success Stories from Our Community',
      description:
        'Celebrating the achievements of entrepreneurs who are making a difference in their industries.',
      time: '1.8K likes',
      author: 'Community Manager',
      image: '/placeholder.svg?height=500&width=800',
      slug: 'entrepreneur-spotlight-stories',
    },
    {
      title: 'Innovation Showcase: Technology Transforming Business',
      description:
        'Discover the latest technological innovations that are revolutionizing how we do business.',
      time: '3.2K likes',
      author: 'Tech Content Team',
      image: '/placeholder.svg?height=500&width=800',
      slug: 'innovation-technology-showcase',
    },
  ]

  const nextSlide = () => {
    if (!isAnimating) {
      setIsAnimating(true)
      setCurrentSlide((prev) => (prev + 1) % socialPosts.length)
      setTimeout(() => setIsAnimating(false), 1000)
    }
  }

  const goToSlide = (index: number) => {
    if (!isAnimating && index !== currentSlide) {
      setIsAnimating(true)
      setCurrentSlide(index)
      setTimeout(() => setIsAnimating(false), 1000)
    }
  }

  useEffect(() => {
    const timer = setInterval(() => {
      nextSlide()
    }, 6000)
    return () => clearInterval(timer)
  }, [])

  return (
    <div className="relative">
      <Card className="overflow-hidden">
        <div className="relative h-[500px]">
          {socialPosts.map((post, index) => {
            const isActive = index === currentSlide
            const isPrev = index === (currentSlide - 1 + socialPosts.length) % socialPosts.length
            const isNext = index === (currentSlide + 1) % socialPosts.length

            let slideClass = 'absolute inset-0 transition-all duration-1000 ease-in-out'

            if (isActive) {
              slideClass += ' opacity-100 transform translate-x-0 scale-100'
            } else if (isNext) {
              slideClass += ' opacity-0 transform translate-x-full scale-95'
            } else if (isPrev) {
              slideClass += ' opacity-0 transform -translate-x-full scale-95'
            } else {
              slideClass += ' opacity-0 transform translate-x-full scale-95'
            }

            return (
              <Link key={index} href={`/social/${post.slug}`}>
                <div className={slideClass}>
                  <div className="relative w-full h-full overflow-hidden cursor-pointer">
                    <Image
                      src={post.image || '/placeholder.svg'}
                      alt={post.title}
                      fill
                      className="object-cover transition-transform duration-1000 ease-out hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
                    <div className="absolute bottom-8 left-8 right-8 text-white">
                      <div
                        className={`transform transition-all duration-1000 delay-300 ${
                          isActive ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
                        }`}
                      >
                        <h3 className="text-3xl font-bold mb-3 leading-tight">{post.title}</h3>
                        <p className="text-gray-200 mb-3 text-lg leading-relaxed">
                          {post.description}
                        </p>
                        <div className="flex items-center space-x-4 text-sm">
                          <span className="bg-purple-600 px-3 py-1 rounded-full">{post.time}</span>
                          <span>•</span>
                          <span className="font-medium">{post.author}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            )
          })}
        </div>
      </Card>

      <div className="flex justify-center space-x-3 mt-6">
        {socialPosts.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`relative overflow-hidden transition-all duration-500 ${
              index === currentSlide
                ? 'w-8 h-3 bg-purple-600 rounded-full'
                : 'w-3 h-3 bg-gray-300 hover:bg-gray-400 rounded-full'
            }`}
          >
            {index === currentSlide && (
              <div className="absolute inset-0 bg-purple-700 rounded-full animate-pulse" />
            )}
          </button>
        ))}
      </div>
    </div>
  )
}

function EventsSlider() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAnimating, setIsAnimating] = useState(false)

  const events = [
    {
      title: 'Global Business Summit 2024: Shaping the Future',
      description:
        'Join industry leaders and innovators for three days of networking, learning, and strategic planning.',
      time: 'March 15-17',
      author: 'Event Organizers',
      image: '/placeholder.svg?height=500&width=800',
      slug: 'global-business-summit-2024',
    },
    {
      title: 'Tech Innovation Workshop: AI and Business Transformation',
      description:
        'Hands-on workshop exploring how artificial intelligence is transforming modern business operations.',
      time: 'February 20',
      author: 'Tech Education Team',
      image: '/placeholder.svg?height=500&width=800',
      slug: 'tech-innovation-workshop',
    },
    {
      title: 'Leadership Masterclass: Building High-Performance Teams',
      description:
        'Learn proven strategies for developing and leading teams that deliver exceptional results.',
      time: 'February 25',
      author: 'Leadership Institute',
      image: '/placeholder.svg?height=500&width=800',
      slug: 'leadership-masterclass',
    },
  ]

  const nextSlide = () => {
    if (!isAnimating) {
      setIsAnimating(true)
      setCurrentSlide((prev) => (prev + 1) % events.length)
      setTimeout(() => setIsAnimating(false), 1000)
    }
  }

  const goToSlide = (index: number) => {
    if (!isAnimating) {
      setIsAnimating(true)
      setCurrentSlide(index)
      setTimeout(() => setIsAnimating(false), 1000)
    }
  }

  useEffect(() => {
    const timer = setInterval(() => {
      nextSlide()
    }, 6000)
    return () => clearInterval(timer)
  }, [])

  return (
    <div className="relative">
      <Card className="overflow-hidden">
        <div className="relative h-[500px]">
          {events.map((event, index) => {
            const isActive = index === currentSlide
            const isPrev = index === (currentSlide - 1 + events.length) % events.length
            const isNext = index === (currentSlide + 1) % events.length

            let slideClass = 'absolute inset-0 transition-all duration-1000 ease-in-out'

            if (isActive) {
              slideClass += ' opacity-100 transform translate-x-0 scale-100'
            } else if (isNext) {
              slideClass += ' opacity-0 transform translate-x-full scale-95'
            } else if (isPrev) {
              slideClass += ' opacity-0 transform -translate-x-full scale-95'
            } else {
              slideClass += ' opacity-0 transform translate-x-full scale-95'
            }

            return (
              <Link key={index} href={`/event/${event.slug}`}>
                <div className={slideClass}>
                  <div className="relative w-full h-full overflow-hidden cursor-pointer">
                    <Image
                      src={event.image || '/placeholder.svg'}
                      alt={event.title}
                      fill
                      className="object-cover transition-transform duration-1000 ease-out hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
                    <div className="absolute bottom-8 left-8 right-8 text-white">
                      <div
                        className={`transform transition-all duration-1000 delay-300 ${
                          isActive ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
                        }`}
                      >
                        <h3 className="text-3xl font-bold mb-3 leading-tight">{event.title}</h3>
                        <p className="text-gray-200 mb-3 text-lg leading-relaxed">
                          {event.description}
                        </p>
                        <div className="flex items-center space-x-4 text-sm">
                          <span className="bg-orange-600 px-3 py-1 rounded-full">{event.time}</span>
                          <span>•</span>
                          <span className="font-medium">{event.author}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Link>
            )
          })}
        </div>
      </Card>

      <div className="flex justify-center space-x-3 mt-6">
        {events.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`relative overflow-hidden transition-all duration-500 ${
              index === currentSlide
                ? 'w-8 h-3 bg-orange-600 rounded-full'
                : 'w-3 h-3 bg-gray-300 hover:bg-gray-400 rounded-full'
            }`}
          >
            {index === currentSlide && (
              <div className="absolute inset-0 bg-orange-700 rounded-full animate-pulse" />
            )}
          </button>
        ))}
      </div>
    </div>
  )
}
