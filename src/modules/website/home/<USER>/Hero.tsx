import { conferenceInfo, conferenceDate } from '../data'
import CountdownCard from './CountdownCard'
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Users, Calendar } from "lucide-react";

export default function Hero() {
  return (
    <section className="relative min-h-screen overflow-hidden">
      {/* Background Image with Overlays */}
      <div className="absolute inset-0">
        {/* Background Video */}
        <video
          autoPlay
          muted
          loop
          playsInline
          className="absolute inset-0 w-full h-full object-cover"
        >
          <source src="/landing-page-video.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>

        {/* White Overlay instead of dark/gradient */}
        <div className="absolute inset-0 bg-white/70" />

        {/* Decorative Pattern Overlay */}
        {/* <div className="absolute inset-0 opacity-10">
          <svg className="w-full h-full" viewBox="0 0 100 100" fill="none">
            <defs>
              <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#7E2518" strokeWidth="0.5" />
              </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#grid)" />
          </svg>
        </div> */}
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-6 py-16">
        {/* Main Hero Container */}
        <div className="flex flex-col lg:flex-row items-center justify-between min-h-[80vh] gap-12">
          {/* Left Side - Enhanced CTA Content */}
          <div className="flex flex-col space-y-8 lg:w-1/2">
            {/* Enhanced Badge */}
            <div className="flex">
              <span className="bg-[#E8B32C] text-[#7E2518] font-bold px-6 py-3 rounded-full text-sm shadow-lg border border-[#7E2518]/20 flex items-center space-x-2">
                <span className="text-lg">🇰🇪</span>
                <span>IKIA Conference 2025</span>
                <div className="w-2 h-2 bg-[#159147] rounded-full animate-pulse"></div>
              </span>
            </div>

            {/* Enhanced Main Title */}
            <div className="flex flex-col space-y-4">
              <h1 className="font-acquire font-semibold text-3xl lg:text-4xl xl:text-5xl text-primary leading-tight">
                {conferenceInfo.title}
              </h1>
              <h2 className="text-xl lg:text-3xl xl:text-4xl text-[#C86E36]">
                {conferenceInfo.subtitle}
              </h2>
              <p className="text-lg lg:text-xl text-gray-700 leading-relaxed bg-white/80 backdrop-blur-sm p-6 rounded-lg border border-gray-200 shadow-sm">
                {conferenceInfo.description}
              </p>
            </div>

            {/* Enhanced Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button className="bg-[#7E2518] hover:bg-[#6B1F14] text-white font-bold px-8 py-4 rounded-lg text-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 flex items-center justify-center space-x-2">
                <span>Register Now</span>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 7l5 5m0 0l-5 5m5-5H6"
                  />
                </svg>
              </button>
              <button className="bg-white border-2 border-[#7E2518] hover:bg-[#7E2518] text-[#7E2518] hover:text-white font-bold px-8 py-4 rounded-lg text-lg transition-all duration-300 shadow-lg hover:shadow-xl flex items-center justify-center space-x-2">
                <span>Learn More</span>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 002 2z"
                  />
                </svg>
              </button>
            </div>

            {/* Enhanced Countdown Card */}
            <div className="flex justify-center lg:justify-start">
              {/* <CountdownCard targetDate={conferenceDate} className="max-w-md w-full" /> */}
            </div>
          </div>

          {/* Right Side - Indigenous Elder Image */}
          <div className="flex lg:w-1/2 justify-center lg:justify-end">
            <div className="relative w-full max-w-lg">
              {/* Main Image Container */}
              <div className="relative">
                {/* Background Glow Effect */}
                <div className="absolute inset-0 bg-[#E8B32C]/10 blur-3xl rounded-full scale-110 animate-pulse"></div>

                {/* Image Container with Glass Effect */}
                <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 border border-gray-200 shadow-xl">
                  {/* Main Elder Image */}
                  <div className="relative">
                    {/* eslint-disable-next-line @next/next/no-img-element */}
                    <img
                      src="/hero-section-image.png"
                      alt="Indigenous Knowledge Keeper - Elder holding traditional artifacts"
                      className="w-full h-auto object-contain drop-shadow-2xl"
                      style={{
                        filter: 'drop-shadow(0 10px 30px rgba(0,0,0,0.2))',
                      }}
                    />

                    {/* Decorative Elements Around Image */}
                    <div className="absolute -top-4 -right-4 w-12 h-12 bg-[#E8B32C]/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-[#E8B32C]/50 animate-bounce">
                      <span className="text-2xl">✨</span>
                    </div>

                    <div className="absolute -bottom-4 -left-4 w-10 h-10 bg-[#159147]/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-[#159147]/50 animate-pulse">
                      <span className="text-xl">🌿</span>
                    </div>

                    <div className="absolute top-1/4 -left-6 w-8 h-8 bg-[#C86E36]/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-[#C86E36]/50 animate-ping">
                      <span className="text-lg">💎</span>
                    </div>
                  </div>

                  {/* Caption Below Image */}
                  <div className="mt-6 text-center space-y-2">
                    <h3 className="font-bold text-xl text-[#7E2518]">Wisdom Keepers</h3>
                    <p className="text-[#C86E36] font-medium text-base">
                      Preserving Heritage • Inspiring Innovation
                    </p>
                    <div className="flex items-center justify-center space-x-2 mt-3">
                      <div className="w-2 h-2 bg-[#159147] rounded-full animate-pulse"></div>
                      <span className="text-gray-600 text-sm">
                        Traditional Knowledge Meets Modern Solutions
                      </span>
                      <div className="w-2 h-2 bg-[#159147] rounded-full animate-pulse"></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Additional Floating Decorative Elements */}
              <div className="absolute -top-8 left-1/4 w-6 h-6 bg-[#E8B32C] rounded-full opacity-60 animate-ping"></div>
              <div className="absolute -bottom-8 right-1/4 w-4 h-4 bg-[#159147] rounded-full opacity-60 animate-pulse"></div>

              {/* Subtle Pattern Background */}
              <div className="absolute inset-0 opacity-5 pointer-events-none">
                <svg className="w-full h-full" viewBox="0 0 200 200" fill="none">
                  <circle
                    cx="100"
                    cy="100"
                    r="80"
                    stroke="#7E2518"
                    strokeWidth="1"
                    strokeDasharray="5,5"
                  />
                  <circle
                    cx="100"
                    cy="100"
                    r="60"
                    stroke="#159147"
                    strokeWidth="0.5"
                    strokeDasharray="3,3"
                  />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Decorative Line */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-[#E8B32C] via-[#C86E36] to-[#159147]"></div>
    </section>
  )
}
