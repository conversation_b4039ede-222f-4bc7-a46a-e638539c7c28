import { Users, FileText, TrendingUp, Leaf, Award, Map, Heart } from 'lucide-react'

const stats = [
  {
    number: '13+',
    label: 'Counties Involved',
    subtitle: 'Across Kenya',
    icon: Map,
  },
  {
    number: '40+',
    label: 'IKIA Assets',
    subtitle: 'Fully Documented',
    icon: FileText,
  },
  {
    number: '1000+',
    label: 'Expected Attendees',
    subtitle: 'Local + Global',
    icon: Users,
  },
  {
    number: '25+',
    label: 'Investment Opportunities',
    subtitle: 'Across 5 Sectors',
    icon: TrendingUp,
  },
]

const categories = [
  { icon: Leaf, color: 'bg-[#159147]' },
  { icon: Award, color: 'bg-[#7E2518]' },
  { icon: FileText, color: 'bg-[#81B1DB]' },
  { icon: Map, color: 'bg-[#159147]' },
  { icon: Heart, color: 'bg-[#E8B32C]' },
  { icon: Users, color: 'bg-[#C86E36]' },
]

export default function StatsSection() {
  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Category Icons */}
        <div className="flex justify-center space-x-4 md:space-x-8 mb-12">
          {categories.map((category, index) => (
            <div
              key={index}
              className={`w-16 h-16 ${category.color} rounded-lg flex items-center justify-center shadow-lg hover:shadow-xl transition-shadow cursor-pointer`}
            >
              <category.icon className="w-8 h-8 text-white" />
            </div>
          ))}
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <div
              key={index}
              className="text-center p-6 bg-gray-50 rounded-lg hover:shadow-lg transition-shadow"
            >
              <div className="mb-4 flex justify-center">
                <stat.icon className="w-12 h-12 text-[#7E2518]" />
              </div>
              <div className="text-3xl md:text-4xl font-bold text-[#7E2518] mb-2">
                {stat.number}
              </div>
              <div className="text-lg font-semibold text-gray-800 mb-1">{stat.label}</div>
              <div className="text-sm text-gray-600">{stat.subtitle}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
