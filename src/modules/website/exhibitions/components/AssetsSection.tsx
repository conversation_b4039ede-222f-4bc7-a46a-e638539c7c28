'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ExternalLink } from 'lucide-react'

const counties = ['MACHAKOS', 'MURANGA', 'TURKANA', 'KAKAMEGA', 'MOMBASA']

const assets = {
  MACHAKOS: [
    {
      id: 1,
      name: 'Machakos Wild Herbal Tea Blend',
      description: 'Traditional tea blend used for digestive health',
      tags: ['Traditional Medicine', 'Plant Systems'],
      category: 'Digestive Health',
    },
    {
      id: 2,
      name: 'Kamba Drought-Resistant Crops',
      description: 'Indigenous crop varieties adapted to arid conditions',
      tags: ['Traditional Medicine', 'Food Systems'],
      category: 'Agricultural Systems',
    },
  ],
  MURANGA: [
    {
      id: 3,
      name: 'Kikuyu Coffee Processing',
      description: 'Traditional coffee processing methods',
      tags: ['Traditional Medicine', 'Food Systems'],
      category: 'Food Processing',
    },
  ],
  TURKANA: [
    {
      id: 4,
      name: 'Pastoralist Water Management',
      description: 'Indigenous water harvesting and management',
      tags: ['Traditional Medicine', 'Water Systems'],
      category: 'Water Management',
    },
  ],
  KAKAMEGA: [
    {
      id: 5,
      name: 'Luhya Medicinal Plants',
      description: 'Forest-based traditional medicine knowledge',
      tags: ['Traditional Medicine', 'Plant Systems'],
      category: 'Medicinal Plants',
    },
  ],
  MOMBASA: [
    {
      id: 6,
      name: 'Coastal Salt Production',
      description: 'Traditional sea salt harvesting methods',
      tags: ['Traditional Medicine', 'Food Systems'],
      category: 'Food Systems',
    },
  ],
}

export default function AssetsSection() {
  const [activeCounty, setActiveCounty] = useState('MACHAKOS')

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#7E2518] mb-4">
            EXPLORE ALL IKIA ASSETS
            <br />
            BY COUNTIES
          </h2>
        </div>

        {/* County Tabs */}
        <div className="flex flex-wrap justify-center mb-8">
          {counties.map((county) => (
            <Button
              key={county}
              onClick={() => setActiveCounty(county)}
              className={`mx-1 my-1 px-6 py-3 ${
                activeCounty === county
                  ? 'bg-[#7E2518] text-white'
                  : 'bg-white text-[#7E2518] border border-[#7E2518] hover:bg-[#7E2518] hover:text-white'
              }`}
            >
              {county}
            </Button>
          ))}
        </div>

        {/* Asset Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {assets[activeCounty as keyof typeof assets]?.map((asset) => (
            <Card key={asset.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <div className="h-48 bg-gray-200 flex items-center justify-center">
                <span className="text-gray-500">Asset Image</span>
              </div>
              <CardContent className="p-6">
                <div className="mb-4">
                  <h3 className="text-xl font-bold text-[#7E2518] mb-2">{asset.name}</h3>
                  <p className="text-gray-600 mb-3">{asset.description}</p>
                </div>

                <div className="flex flex-wrap gap-2 mb-4">
                  {asset.tags.map((tag, tagIndex) => (
                    <Badge
                      key={tagIndex}
                      variant="secondary"
                      className="bg-[#E8B32C] text-[#7E2518]"
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>

                <div className="flex items-center justify-between">
                  <Badge className="bg-[#159147] text-white">{asset.category}</Badge>
                  <Button size="sm" className="bg-[#7E2518] hover:bg-[#6B1F14]">
                    <ExternalLink className="w-4 h-4 mr-2" />
                    View Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
