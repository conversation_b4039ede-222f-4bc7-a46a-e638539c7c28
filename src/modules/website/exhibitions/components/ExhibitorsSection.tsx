'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ExternalLink } from 'lucide-react'

const counties = ['MACHAKOS', 'MURANGA', 'TURKANA', 'KAKAMEGA', 'MOMBASA']

const exhibitors = {
  MACHAKOS: [
    {
      id: 1,
      name: 'Machakos Wild Herbal Tea Blend',
      description: 'Traditional tea blend used for digestive health',
      tags: ['Traditional Medicine', 'Plant Systems'],
      category: 'Digestive Health',
      image: '/api/placeholder/300/200',
    },
    {
      id: 2,
      name: 'Kamba Traditional Basketry',
      description: 'Handwoven baskets using indigenous techniques',
      tags: ['Traditional Crafts', 'Cultural Heritage'],
      category: 'Handicrafts',
      image: '/api/placeholder/300/200',
    },
  ],
  MURANGA: [
    {
      id: 3,
      name: 'Kikuyu Traditional Honey',
      description: 'Pure honey from indigenous beekeeping practices',
      tags: ['Traditional Medicine', 'Food Systems'],
      category: 'Digestive Health',
      image: '/api/placeholder/300/200',
    },
  ],
  TURKANA: [
    {
      id: 4,
      name: 'Turkana Livestock Medicine',
      description: 'Traditional veterinary practices for livestock',
      tags: ['Traditional Medicine', 'Livestock'],
      category: 'Animal Health',
      image: '/api/placeholder/300/200',
    },
  ],
  KAKAMEGA: [
    {
      id: 5,
      name: 'Luhya Forest Conservation',
      description: 'Indigenous forest management techniques',
      tags: ['Environmental', 'Conservation'],
      category: 'Forest Management',
      image: '/api/placeholder/300/200',
    },
  ],
  MOMBASA: [
    {
      id: 6,
      name: 'Swahili Coastal Fishing',
      description: 'Traditional coastal fishing methods and tools',
      tags: ['Traditional Practices', 'Marine'],
      category: 'Fisheries',
      image: '/api/placeholder/300/200',
    },
  ],
}

export default function ExhibitorsSection() {
  const [activeCounty, setActiveCounty] = useState('MACHAKOS')

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-[#7E2518] mb-4">
            EXPLORE ALL EXHIBITORS
            <br />
            BY COUNTIES
          </h2>
        </div>

        {/* County Tabs */}
        <div className="flex flex-wrap justify-center mb-8">
          {counties.map((county) => (
            <Button
              key={county}
              onClick={() => setActiveCounty(county)}
              className={`mx-1 my-1 px-6 py-3 ${
                activeCounty === county
                  ? 'bg-[#7E2518] text-white'
                  : 'bg-white text-[#7E2518] border border-[#7E2518] hover:bg-[#7E2518] hover:text-white'
              }`}
            >
              {county}
            </Button>
          ))}
        </div>

        {/* Exhibitor Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {exhibitors[activeCounty as keyof typeof exhibitors]?.map((exhibitor) => (
            <Card key={exhibitor.id} className="overflow-hidden hover:shadow-lg transition-shadow">
              <div className="h-48 bg-gray-200 flex items-center justify-center">
                <span className="text-gray-500">Exhibitor Image</span>
              </div>
              <CardContent className="p-6">
                <div className="mb-4">
                  <h3 className="text-xl font-bold text-[#7E2518] mb-2">{exhibitor.name}</h3>
                  <p className="text-gray-600 mb-3">{exhibitor.description}</p>
                </div>

                <div className="flex flex-wrap gap-2 mb-4">
                  {exhibitor.tags.map((tag, tagIndex) => (
                    <Badge
                      key={tagIndex}
                      variant="secondary"
                      className="bg-[#E8B32C] text-[#7E2518]"
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>

                <div className="flex items-center justify-between">
                  <Badge className="bg-[#159147] text-white">{exhibitor.category}</Badge>
                  <Button size="sm" className="bg-[#7E2518] hover:bg-[#6B1F14]">
                    <ExternalLink className="w-4 h-4 mr-2" />
                    View Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}
